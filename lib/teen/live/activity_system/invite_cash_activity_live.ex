defmodule Teen.Live.ActivitySystem.InviteCashActivityLive do
  @moduledoc """
  邀请奖励活动管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.InviteCashActivity,
      create_action: :create_with_configs,
      update_action: :update_with_configs
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def plural_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:index, :show]
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "标题",
        searchable: true
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "活动描述",
        except: [:index]
      },
      total_reward: %{
        module: Backpex.Fields.Number,
        label: "奖励总金额（分）"
      },
      initial_min: %{
        module: Backpex.Fields.Number,
        label: "初始最小值（分）"
      },
      initial_max: %{
        module: Backpex.Fields.Number,
        label: "初始最大值（分）"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      invite_reward_configs: %{
        module: Backpex.Fields.InlineCRUD,
        label: "奖励配置",
        type: :assoc,
        except: [:index],
        resource: Teen.ActivitySystem.InviteRewardConfig,
        child_fields: [
          round_number: %{
            module: Backpex.Fields.Number,
            label: "轮次",
            class: "w-20",
            default: fn _assigns -> 1 end
          },
          task_type: %{
            module: Backpex.Fields.Select,
            label: "任务类型",
            options: [
              {"邀请注册", :invite_register},
              {"邀请充值", :invite_recharge},
              {"邀请游戏", :invite_play},
              {"邀请留存", :invite_retention}
            ],
            class: "w-32",
            default: fn _assigns -> :invite_register end
          },
          reward_type: %{
            module: Backpex.Fields.Select,
            label: "奖励类型",
            options: [
              {"金币", :coins},
              {"积分", :points},
              {"现金", :cash},
              {"转盘次数", :wheel_spins},
              {"道具", :items}
            ],
            class: "w-28",
            default: fn _assigns -> :coins end
          },
          min_reward: %{
            module: Backpex.Fields.Number,
            label: "最小奖励（分）",
            class: "w-32"
          },
          max_reward: %{
            module: Backpex.Fields.Number,
            label: "最大奖励（分）",
            class: "w-32"
          },
          required_progress: %{
            module: Backpex.Fields.Number,
            label: "所需进度",
            class: "w-24",
            default: fn _assigns -> 1 end
          },
          probability: %{
            module: Backpex.Fields.Number,
            label: "概率",
            class: "w-24",
            default: fn _assigns -> 1.0 end
          },
          sort_order: %{
            module: Backpex.Fields.Number,
            label: "排序",
            class: "w-20",
            default: fn _assigns -> 0 end
          }
        ]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:index, :show]
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(_assigns, _action, _item) do
    true
  end
end
