defmodule Teen.ActivitySystem.Activity do
  @moduledoc """
  活动资源

  管理系统中的各种活动，包括刮刮卡、转盘等活动
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  require Logger

  admin do
    table_columns [
      :id,
      :name,
      :type,
      :status,
      :start_date,
      :end_date,
      :inserted_at,
      :updated_at
    ]
  end

  postgres do
    table "activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_type
    define :list_active
    define :list_by_status
    define :activate
    define :deactivate
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :name,
        :description,
        :type,
        :status,
        :start_date,
        :end_date,
        :config_data
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :active)
      end
    end

    update :update do
      require_atomic? false
      accept [
        :name,
        :description,
        :status,
        :start_date,
        :end_date,
        :config_data
      ]
    end

    read :list_by_type do
      argument :type, :atom, allow_nil?: false
      filter expr(type == ^arg(:type))
      prepare build(sort: [desc: :created_at])
    end

    read :list_active do
      filter expr(status == :active)
      prepare build(sort: [desc: :created_at])
    end

    read :list_by_status do
      argument :status, :atom, allow_nil?: false
      filter expr(status == ^arg(:status))
      prepare build(sort: [desc: :created_at])
    end

    update :activate do
      accept []
      change set_attribute(:status, :active)
    end

    update :deactivate do
      accept []
      change set_attribute(:status, :inactive)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "活动名称"
      constraints [
        max_length: 100,
        min_length: 2,
        trim?: true
      ]
    end

    attribute :description, :string do
      public? true
      description "活动描述"
      constraints [
        max_length: 500,
        trim?: true
      ]
    end

    attribute :type, :atom do
      allow_nil? false
      public? true
      description "活动类型"
      constraints one_of: [:scratch_card, :wheel, :daily_check, :task, :promotion]
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "活动状态"
      constraints one_of: [:active, :inactive, :draft, :expired]
      default :draft
    end

    attribute :start_date, :date do
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      public? true
      description "结束日期"
    end

    attribute :config_data, :map do
      allow_nil? false
      public? true
      description "配置数据"
      default %{}
    end

    timestamps()
  end

  relationships do
    has_many :scratch_card_activities, Teen.ActivitySystem.ScratchCardActivity do
      public? true
      destination_attribute :activity_id
      description "关联的刮刮卡活动配置"
    end

    has_many :recharge_wheels, Teen.ActivitySystem.RechargeWheel do
      public? true
      destination_attribute :activity_id
    end
  end

  identities do
    identity :unique_name, [:name] do
      message "活动名称不能重复"
    end
  end

  validations do
    validate present([:name, :type])

    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      if start_date && end_date && Date.compare(start_date, end_date) == :gt do
        {:error, field: :end_date, message: "结束日期不能早于开始日期"}
      else
        :ok
      end
    end
  end
end
