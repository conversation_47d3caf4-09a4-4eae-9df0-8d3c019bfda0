defmodule Teen.ActivitySystem.InviteCashActivity do
  @moduledoc """
  拼多多邀请提现（Free Cash）资源

  管理邀请活动配置
  包括奖励总金额、初始奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "invite_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :create_with_configs
    define :read
    define :get_with_configs
    define :list_with_configs
    define :list_active_with_configs
    define :update
    define :update_with_configs
    define :destroy
    define :list_active_activities
    define :enable_activity
    define :disable_activity
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    update :update do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false
    end

    create :create_with_configs do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      argument :reward_configs, {:array, :map} do
        allow_nil? false
        description "奖励配置列表"
      end

      change fn changeset, context ->
        changeset = changeset |> Ash.Changeset.change_attribute(:status, :enabled)

        # 在事务中创建活动和奖励配置
        changeset
        |> Ash.Changeset.after_action(fn changeset, activity ->
          reward_configs = Ash.Changeset.get_argument(changeset, :reward_configs) || []

          # 为每个奖励配置创建记录
          results = Enum.map(reward_configs, fn config ->
            config_params = Map.merge(config, %{
              "activity_id" => activity.id
            })

            Ash.create(Teen.ActivitySystem.InviteRewardConfig, config_params, action: :create)
          end)

          # 检查是否有创建失败的配置
          case Enum.find(results, fn result -> match?({:error, _}, result) end) do
            nil -> {:ok, activity}
            {:error, error} -> {:error, error}
          end
        end)
      end
    end

    update :update_with_configs do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false

      argument :reward_configs, {:array, :map} do
        allow_nil? true
        description "奖励配置列表"
      end

      argument :replace_configs, :boolean do
        allow_nil? true
        default false
        description "是否替换所有现有配置（true=替换，false=合并更新）"
      end

      change fn changeset, context ->
        reward_configs = Ash.Changeset.get_argument(changeset, :reward_configs)
        replace_configs = Ash.Changeset.get_argument(changeset, :replace_configs) || false

        if reward_configs do
          changeset
          |> Ash.Changeset.after_action(fn changeset, activity ->
            if replace_configs do
              # 替换模式：删除所有现有配置，然后创建新的
              case Teen.ActivitySystem.InviteRewardConfig.list_by_activity(activity_id: activity.id) do
                {:ok, existing_configs} ->
                  # 删除现有配置
                  delete_results = Enum.map(existing_configs, fn config ->
                    Ash.destroy(config)
                  end)

                  # 检查删除是否成功
                  case Enum.find(delete_results, fn result -> match?({:error, _}, result) end) do
                    nil ->
                      # 创建新配置
                      create_results = Enum.map(reward_configs, fn config ->
                        config_params = Map.merge(config, %{
                          "activity_id" => activity.id
                        })
                        Ash.create(Teen.ActivitySystem.InviteRewardConfig, config_params, action: :create)
                      end)

                      case Enum.find(create_results, fn result -> match?({:error, _}, result) end) do
                        nil -> {:ok, activity}
                        {:error, error} -> {:error, error}
                      end

                    {:error, error} -> {:error, error}
                  end

                {:error, error} -> {:error, error}
              end
            else
              # 合并模式：更新现有配置或创建新配置
              case Teen.ActivitySystem.InviteRewardConfig.list_by_activity(activity_id: activity.id) do
                {:ok, existing_configs} ->
                  existing_map = Map.new(existing_configs, fn config -> {config.round_number, config} end)

                  results = Enum.map(reward_configs, fn config ->
                    round_number = Map.get(config, "round_number") || Map.get(config, :round_number)

                    case Map.get(existing_map, round_number) do
                      nil ->
                        # 创建新配置
                        config_params = Map.merge(config, %{
                          "activity_id" => activity.id
                        })
                        Ash.create(Teen.ActivitySystem.InviteRewardConfig, config_params, action: :create)

                      existing_config ->
                        # 更新现有配置
                        Ash.update(existing_config, config)
                    end
                  end)

                  case Enum.find(results, fn result -> match?({:error, _}, result) end) do
                    nil -> {:ok, activity}
                    {:error, error} -> {:error, error}
                  end

                {:error, error} -> {:error, error}
              end
            end
          end)
        else
          # 如果没有奖励配置参数，只更新活动本身
          changeset
        end
      end
    end

    read :list_active_activities do
      filter expr(status == :enabled)
    end

    read :get_with_configs do
      get? true
      prepare build(load: [:invite_reward_configs])
    end

    read :list_with_configs do
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_active_with_configs do
      filter expr(status == :enabled)
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    update :enable_activity do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_activity do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :total_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励总金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_min, :decimal do
      allow_nil? false
      public? true
      description "初始最小值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_max, :decimal do
      allow_nil? false
      public? true
      description "初始最大值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end



  relationships do
    has_many :invite_reward_configs, Teen.ActivitySystem.InviteRewardConfig do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end
  end
end
