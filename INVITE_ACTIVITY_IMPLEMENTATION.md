# 邀请奖励活动动态配置功能实现

## 概述

本次实现为邀请奖励活动添加了动态配置功能，允许在创建活动时同时配置一个或多个邀请奖励配置。

## 主要修改

### 1. 修复了 InviteCashActivity 资源中的问题

**文件**: `lib/teen/resources/activity_system/invite_cash_activity.ex`

**修复内容**:
- 修复了 `update_with_configs` 动作中调用 `Teen.ActivitySystem.InviteRewardConfig.list_by_activity` 的参数格式错误
- 将 `%{activity_id: activity.id}` 修改为 `activity_id: activity.id`

### 2. 更新了 InviteCashActivityLive 页面

**文件**: `lib/teen/live/activity_system/invite_cash_activity_live.ex`

**主要改动**:

#### 2.1 配置了专用的创建和更新动作
```elixir
adapter_config: [
  resource: Teen.ActivitySystem.InviteCashActivity,
  create_action: :create_with_configs,
  update_action: :update_with_configs
]
```

#### 2.2 移除了不属于活动本身的字段
移除了以下字段（这些字段属于奖励配置，不属于活动本身）：
- `task_type`
- `reward_amount` 
- `reward_type`

#### 2.3 添加了 InlineCRUD 字段用于动态配置奖励
```elixir
invite_reward_configs: %{
  module: Backpex.Fields.InlineCRUD,
  label: "奖励配置",
  type: :assoc,
  except: [:index],
  child_fields: [
    round_number: %{
      module: Backpex.Fields.Number,
      label: "轮次",
      class: "w-20",
      default: fn _assigns -> 1 end
    },
    task_type: %{
      module: Backpex.Fields.Select,
      label: "任务类型",
      options: [
        {"邀请注册", :invite_register},
        {"邀请充值", :invite_recharge},
        {"邀请游戏", :invite_play},
        {"邀请留存", :invite_retention}
      ],
      class: "w-32",
      default: fn _assigns -> :invite_register end
    },
    reward_type: %{
      module: Backpex.Fields.Select,
      label: "奖励类型",
      options: [
        {"金币", :coins},
        {"积分", :points},
        {"现金", :cash},
        {"转盘次数", :wheel_spins},
        {"道具", :items}
      ],
      class: "w-28",
      default: fn _assigns -> :coins end
    },
    min_reward: %{
      module: Backpex.Fields.Number,
      label: "最小奖励（分）",
      class: "w-32"
    },
    max_reward: %{
      module: Backpex.Fields.Number,
      label: "最大奖励（分）",
      class: "w-32"
    },
    required_progress: %{
      module: Backpex.Fields.Number,
      label: "所需进度",
      class: "w-24",
      default: fn _assigns -> 1 end
    },
    probability: %{
      module: Backpex.Fields.Number,
      label: "概率",
      class: "w-24",
      default: fn _assigns -> 1.0 end
    },
    sort_order: %{
      module: Backpex.Fields.Number,
      label: "排序",
      class: "w-20",
      default: fn _assigns -> 0 end
    }
  ]
}
```

## 功能特性

### 1. 动态添加奖励配置
- 在创建或编辑邀请活动时，可以动态添加多个奖励配置
- 每个奖励配置包含：轮次、任务类型、奖励类型、奖励范围、所需进度、概率、排序等

### 2. 内联编辑
- 使用 Backpex 的 InlineCRUD 字段实现内联编辑
- 支持添加、编辑、删除奖励配置
- 提供友好的用户界面

### 3. 默认值设置
- 为各个字段设置了合理的默认值
- 简化用户操作流程

### 4. 数据验证
- 利用 Ash 资源的验证功能确保数据完整性
- 支持必填字段验证、数值范围验证等

## 使用方法

### 1. 访问管理界面
访问 `/admin/invite-cash-activities` 进入邀请奖励活动管理页面

### 2. 创建新活动
1. 点击"新建"按钮
2. 填写活动基本信息（标题、描述、奖励总金额等）
3. 在"奖励配置"部分点击"+"按钮添加奖励配置
4. 为每个奖励配置设置相应参数
5. 保存活动

### 3. 编辑现有活动
1. 在列表中点击要编辑的活动
2. 修改活动信息或奖励配置
3. 可以添加新的奖励配置或删除现有配置
4. 保存更改

## 技术实现

### 1. 后端动作
- `create_with_configs`: 创建活动时同时创建奖励配置
- `update_with_configs`: 更新活动时支持替换或合并奖励配置

### 2. 前端组件
- 使用 Backpex.Fields.InlineCRUD 实现动态表单
- 支持关联数据的内联编辑

### 3. 数据关系
- InviteCashActivity 与 InviteRewardConfig 为一对多关系
- 通过 activity_id 外键关联

## 注意事项

1. 奖励配置的 `round_number` 和 `activity_id` 组合必须唯一
2. 概率值必须在 0-1 之间
3. 所需进度必须大于 0
4. 奖励金额必须为非负数

## 测试建议

建议通过以下方式测试功能：

1. **创建测试**: 创建一个包含多个奖励配置的活动
2. **编辑测试**: 修改现有活动的奖励配置
3. **删除测试**: 删除某些奖励配置
4. **验证测试**: 确保数据验证规则正常工作
5. **界面测试**: 验证用户界面的易用性

## 后续优化

1. 可以考虑添加批量导入奖励配置的功能
2. 可以添加奖励配置模板功能
3. 可以优化用户界面的响应式设计
4. 可以添加更多的数据验证规则
